#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
# use for access seatunnel from outside system via rest api
apiVersion: v1
kind: Service
metadata:
  name: {{ include "seatunnel.fullname" . }}-master
  labels:
    {{- include "seatunnel.master.labels" . | nindent 4 }}
  namespace: {{ .Values.namespace }}
spec:
  clusterIP: "None"
  ports:
    - name: "master-port"
      port: 8080
      targetPort: 8080
      protocol: TCP
  selector:
    {{- include "seatunnel.master.labels" . | nindent 4 }}