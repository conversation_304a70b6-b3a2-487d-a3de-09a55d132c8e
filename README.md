<p><a href="https://github.com/potiuk/get-workflow-origin/actions">
<img alt="get-workflow-origin status"
    src="https://github.com/potiuk/get-workflow-origin/workflows/Test%20the%20build/badge.svg"></a>

# Get Workflow Origin action


<!-- START doctoc generated TOC please keep comment here to allow auto update -->
<!-- DON'T EDIT THIS SECTION, INSTEAD RE-RUN doctoc TO UPDATE -->
**Table of Contents**  *generated with [DocToc](https://github.com/thlorenz/doctoc)*

- [Context and motivation](#context-and-motivation)
- [Inputs and outputs](#inputs-and-outputs)
  - [Inputs](#inputs)
  - [Outputs](#outputs)
- [Examples](#examples)
    - [Workflow Run event](#workflow-run-event)
    - [Workflow Run event](#workflow-run-event-1)
  - [Development environment](#development-environment)
  - [License](#license)

<!-- <PERSON><PERSON> doctoc generated TOC please keep comment here to allow auto update -->

# Context and motivation

Get Workflow Origin is an action that provides information about the pull requests that triggered the
workflow for the `pull_request` and `pull_request_review` events or for the `workflow_run` event
that is triggered by one of those events.

Often in those events you want to get more information about the source run than the
one provided directly via GitHub context.

For example, you would like to know what is the merge commit generated by pull request in case
the workflow is triggered by a pull request, or labels associated with the Pull Request.

This action provides outputs that give that information. You should add this action as first
one in your workflow and then you will be able to use those outputs using 'needs' dependency.

The `sourceRunId` input should not be specified in case of the `pull_request` event, but it should
be set to `${{ github.event.workflow_run.id }}` in case of the `workflow_run` event.

# Inputs and outputs

## Inputs

| Input                   | Required | Default      | Comment                                                                                                                                                                                                          |
|-------------------------|----------|--------------|-----------------------------------------------------------------------------------------------------|
| `token`                 | yes      |              | The github token passed from `${{ secrets.GITHUB_TOKEN }}`                                          |
| `sourceRunId`           | no       |              | In case of 'workflow_run' event it should be set to `${{ github.event.workflow_run.id }}`           |

## Outputs

| Output               | No `sourceRunId` specified                                                          | The `sourceRunId` set to `${{ github.event.workflow_run.id }}`                                        |
|----------------------|-------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------|
| `sourceHeadRepo`     | Current repository. Format: `owner/repo`                                            | Repository of the run that triggered this `workflow_run`. Format: `owner/repo`                        |
| `sourceHeadBranch`   | Current branch.                                                                     | Branch of the run that triggered this `workflow_run`. Might be forked repo, if it is a pull_request.  |
| `sourceHeadSha`      | Current commit SHA: `{{ github.sha }}`                                              | Commit sha of the run that triggered this `workflow_run`.                                             |
| `mergeCommitSha`     | Merge commit SHA if PR-triggered event.                                             | Merge commit SHA if PR-triggered event.                                                               |
| `targetCommitSha`    | Target commit SHA (merge if present, otherwise source).                             | Target commit SHA (merge if present, otherwise source).                                               |
| `pullRequestNumber`  | Number of the associated Pull Request (if PR triggered)                             | Number of the associated Pull Request (if PR triggered)                                               |
| `pullRequestLabels`  | Stringified JSON array of Labels of the associated Pull Request (if PR triggered)   | Stringified JSON array of Labels of the associated Pull Request (if PR triggered)                     |
| `targetBranch`       | Target branch of the pull request or target branch for push                         | Target branch of the pull request or target branch for push                                           |
| `sourceEvent`        | Current event: ``${{ github.event }}``                                              | Event of the run that triggered this `workflow_run`                                                   |

# Examples

### Workflow Run event

```yaml
name: Get information
on:
  pull_request:
    branches: ['main']

jobs:
  get-info:
    name: "Get information about the source run"
    runs-on: ubuntu-latest
    outputs:
      sourceHeadRepo: ${{ steps.workflow-run-info.outputs.sourceHeadRepo }}
      sourceHeadBranch: ${{ steps.workflow-run-info.outputs.sourceHeadBranch }}
      sourceHeadSha: ${{ steps.workflow-run-info.outputs.sourceHeadSha }}
      mergeCommitSha: ${{ steps.workflow-run-info.outputs.mergeCommitSha }}
      targetCommitSha: ${{ steps.workflow-run-info.outputs.targetCommitSha }}
      pullRequestNumber: ${{ steps.workflow-run-info.outputs.pullRequestNumber }}
      pullRequestLabels: ${{ steps.workflow-run-info.outputs.pullRequestLabels }}
      targetBranch: ${{ steps.source-run-info.outputs.targetBranch }}
      sourceEvent: ${{ steps.workflow-run-info.outputs.sourceEvent }}
    steps:
      - name: "Get information about the current run"
        uses: potiuk/get-workflow-origin@v1_1
        id: workflow-run-info
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
```


### Workflow Run event

```yaml
name: Build
on:
  workflow_run:
    workflows: ['CI']
    types: ['requested']

jobs:
  get-info:
    name: "Get information about the source run"
    runs-on: ubuntu-latest
    outputs:
      sourceHeadRepo: ${{ steps.source-run-info.outputs.sourceHeadRepo }}
      sourceHeadBranch: ${{ steps.source-run-info.outputs.sourceHeadBranch }}
      sourceHeadSha: ${{ steps.source-run-info.outputs.sourceHeadSha }}
      mergeCommitSha: ${{ steps.source-run-info.outputs.mergeCommitSha }}
      targetCommitSha: ${{ steps.source-run-info.outputs.targetCommitSha }}
      pullRequestNumber: ${{ steps.source-run-info.outputs.pullRequestNumber }}
      pullRequestLabels: ${{ steps.source-run-info.outputs.pullRequestLabels }}
      targetBranch: ${{ steps.source-run-info.outputs.targetBranch }}
      sourceEvent: ${{ steps.source-run-info.outputs.sourceEvent }}
    steps:
      - name: "Get information about the origin 'CI' run"
        uses: potiuk/get-workflow-origin@v1_1
        id: source-run-info
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          sourceRunId: ${{ github.event.workflow_run.id }}```
```


## Development environment

It is highly recommended tu use [pre commit](https://pre-commit.com). The pre-commits
installed via pre-commit tool handle automatically linting (including automated fixes) as well
as building and packaging Javascript index.js from the main.ts Typescript code, so you do not have
to run it yourself.

## License
[MIT License](LICENSE) covers the scripts and documentation in this project.
