# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

github:
  description: SeaTunnel is a multimodal, high-performance, distributed, massive data integration tool.
  homepage: https://seatunnel.apache.org/
  labels:
    - data-integration
    - multimodal
    - llm
    - embeddings
    - change-data-capture
    - cdc
    - high-performance
    - offline
    - real-time
    - batch
    - streaming
    - data-ingestion
    - apache
    - elt
  enabled_merge_buttons:
    squash: true
    merge: false
    rebase: false
  protected_branches:
    dev:
      required_status_checks:
        strict: true
      required_pull_request_reviews:
        dismiss_stale_reviews: true
        required_approving_review_count: 2

notifications:
  commits:      <EMAIL>
  issues:       <EMAIL>
  pullrequests: <EMAIL>
  pullrequests_status:  <EMAIL>
  pullrequests_comment: <EMAIL>
