# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: publish-helm-chart

on:
  push:
    tags:
      - '*'
    paths-ignore:
      - 'docs/**'
      - '**/*.md'

env:
  DOCKER_USERNAME: ${{ secrets.DOCKERHUB_USER }}
  DOCKER_PASSWORD: ${{ secrets.DOCKERHUB_TOKEN }}
  DOCKER_REGISTRY: docker.io
  HUB: registry-1.docker.io/apache

jobs:
  build:
    if: github.repository == 'apache/seatunnel'
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v4
      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.DOCKER_REGISTRY }}
          username: ${{ env.DOCKER_USERNAME }}
          password: ${{ env.DOCKER_PASSWORD }}
      - name: Publish Helm Chart
        working-directory: deploy/kubernetes
        run: |
          helm dep up seatunnel
          helm package seatunnel
          helm push seatunnel-helm-*.tgz oci://${{ env.HUB }}
