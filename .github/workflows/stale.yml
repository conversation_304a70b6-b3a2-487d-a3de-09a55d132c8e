# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


# https://github.com/actions/stale
name: 'Close stale issues and PRs'
on:
  schedule:
    - cron: '0 0 * * *'
permissions:
  # Stale recommended permissions
  pull-requests: write
  issues: write
jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v4
        with:
          # Stale Issues
          days-before-issue-stale: -1
          days-before-issue-close: -1
          # We do not stale Issues with label `Waiting for reply`, `Waiting for code update`,`Waiting for users feedback`, `New feature` and `STIP`
          exempt-issue-labels: 'Waiting for reply,Waiting for code update,Waiting for users feedback,New feature,STIP,security'
          stale-issue-message: >
            This issue has been automatically marked as stale because it has not had recent activity
            for 30 days. It will be closed in next 7 days if no further activity occurs.
          close-issue-message: >
            This issue has been closed because it has not received response for too long time. You could
            reopen it if you encountered similar problems in the future.
          # Stale PRs
          days-before-pr-stale: 120
          days-before-pr-close: 7
          stale-pr-message: >
            This pull request has been automatically marked as stale because it has not had recent
            activity for 120 days. It will be closed in 7 days if no further activity occurs.
          close-pr-message: >
            This pull request has been closed because it has not had recent activity. You could reopen it
            if you try to continue your work, and anyone who are interested in it are encouraged to continue
            work on this pull request.
          remove-pr-stale-when-updated: true
          remove-issue-stale-when-updated: true
          operations-per-run: 1000
