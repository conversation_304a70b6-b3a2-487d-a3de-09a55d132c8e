#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the 'License'); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an 'AS IS' BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

name: Schedule Backend
on:
  schedule:
    - cron: '0 16 * * *'

concurrency:
  group: schedule-backend-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: false

jobs:
  call-build-and-test:
    permissions:
      packages: write
    name: Run
    uses: ./.github/workflows/backend.yml
    with:
      TEST_IN_PR: false
