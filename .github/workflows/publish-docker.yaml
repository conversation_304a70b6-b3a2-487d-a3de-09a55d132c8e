# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: publish-docker

on:
  push:
    tags:
      - '*'
    paths-ignore:
      - 'docs/**'
      - '**/*.md'

env:
  DOCKER_USERNAME: ${{ secrets.DOCKERHUB_USER }}
  DOCKER_PASSWORD: ${{ secrets.DOCKERHUB_TOKEN }}

jobs:
  build:
    if: github.repository == 'apache/seatunnel'
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    timeout-minutes: 60
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true
      - name: free disk space
        run: tools/github/free_disk_space.sh
      - uses: actions/checkout@v4
      - name: Cache local Maven repository
        uses: actions/cache@v4
        with:
          path: ~/.m2/repository
          key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-maven-
      - name: Set up JDK 1.8
        uses: actions/setup-java@v2
        with:
          java-version:  8
          distribution: 'adopt'

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          username: ${{ env.DOCKER_USERNAME }}
          password: ${{ env.DOCKER_PASSWORD }}
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build and push docker images
        env:
          MAVEN_OPTS: -Xmx4096m
        run: |
          ./mvnw -B clean install \
          -Dmaven.test.skip=true \
          -Dmaven.javadoc.skip=true \
          -Dlicense.skipAddThirdParty=true \
          -D"docker.build.skip"=false \
          -D"docker.verify.skip"=false \
          -D"docker.push.skip"=false \
          -D"skip.spotless"=true \
          -Dmaven.deploy.skip \
          --no-snapshot-updates \
          -Pdocker,seatunnel