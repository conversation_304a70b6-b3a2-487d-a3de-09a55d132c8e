#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  parallelism = 1
  job.mode = "STREAMING"
  checkpoint.interval = 5000
}

source {
  # This is a example source plugin **only for test and demonstrate the feature source plugin**
    FakeSource {
      plugin_output = "fake"
       row.num = 100
       split.num = 5
       split.read-interval = 3000
       parallelism = 1
      schema = {
        fields {
          name = "string"
          age = "int"
        }
      }
      parallelism = 1
    }
}

transform {
}

sink {
  LocalFile {
    path="/tmp/hive/warehouse/test3"
    field_delimiter="\t"
    row_delimiter="\n"
    partition_by=["age"]
    partition_dir_expression="${k0}=${v0}"
    is_partition_field_write_in_file=true
    file_name_expression="${transactionId}_${now}"
    file_format_type="text"
    sink_columns=["name","age"]
    filename_time_format="yyyy.MM.dd"
    is_enable_transaction=true
    save_mode="error"

  }
}