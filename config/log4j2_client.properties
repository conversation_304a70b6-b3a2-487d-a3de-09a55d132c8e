################################################################################
#  Licensed to the Apache Software Foundation (ASF) under one
#  or more contributor license agreements.  See the NOTICE file
#  distributed with this work for additional information
#  regarding copyright ownership.  The ASF licenses this file
#  to you under the Apache License, Version 2.0 (the
#  "License"); you may not use this file except in compliance
#  with the License.  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
# limitations under the License.
################################################################################

# The minimum amount of time, in seconds, that must elapse before the file configuration is checked for changes.
monitorInterval = 60

property.file_path = ${sys:seatunnel.logs.path:-/tmp/seatunnel/logs}
property.file_name = ${sys:seatunnel.logs.file_name:-seatunnel}
property.file_split_size = 100MB
property.file_count = 100
property.file_ttl = 7d

rootLogger.level = INFO

############################ log output to console #############################
rootLogger.appenderRef.consoleStdout.ref = consoleStdoutAppender
rootLogger.appenderRef.consoleStderr.ref = consoleStderrAppender
############################ log output to console #############################
############################ log output to file    #############################
#rootLogger.appenderRef.file.ref = fileAppender
############################ log output to file    #############################

appender.consoleStdout.name = consoleStdoutAppender
appender.consoleStdout.type = CONSOLE
appender.consoleStdout.target = SYSTEM_OUT
appender.consoleStdout.layout.type = PatternLayout
appender.consoleStdout.layout.pattern = %d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%-30.30c{1.}] [%t] - %m%n
appender.consoleStdout.filter.acceptLtWarn.type = ThresholdFilter
appender.consoleStdout.filter.acceptLtWarn.level = WARN
appender.consoleStdout.filter.acceptLtWarn.onMatch = DENY
appender.consoleStdout.filter.acceptLtWarn.onMismatch = ACCEPT

appender.consoleStderr.name = consoleStderrAppender
appender.consoleStderr.type = CONSOLE
appender.consoleStderr.target = SYSTEM_ERR
appender.consoleStderr.layout.type = PatternLayout
appender.consoleStderr.layout.pattern = %d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%-30.30c{1.}] [%t] - %m%n
appender.consoleStderr.filter.acceptGteWarn.type = ThresholdFilter
appender.consoleStderr.filter.acceptGteWarn.level = WARN
appender.consoleStderr.filter.acceptGteWarn.onMatch = ACCEPT
appender.consoleStderr.filter.acceptGteWarn.onMismatch = DENY

#appender.file.name = fileAppender
#appender.file.type = RollingFile
#appender.file.fileName = ${file_path}/${file_name}.log
#appender.file.filePattern = ${file_path}/${file_name}.log.%d{yyyy-MM-dd}-%i
#appender.file.append = true
#appender.file.layout.type = PatternLayout
#appender.file.layout.pattern = %d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%-30.30c{1.}] [%t] - %m%n
#appender.file.policies.type = Policies
#appender.file.policies.time.type = TimeBasedTriggeringPolicy
#appender.file.policies.time.modulate = true
#appender.file.policies.size.type = SizeBasedTriggeringPolicy
#appender.file.policies.size.size = ${file_split_size}
#appender.file.strategy.type = DefaultRolloverStrategy
#appender.file.strategy.fileIndex = nomax
#appender.file.strategy.action.type = Delete
#appender.file.strategy.action.basepath = ${file_path}
#appender.file.strategy.action.maxDepth = 1
#appender.file.strategy.action.condition.type = IfFileName
#appender.file.strategy.action.condition.glob = ${file_name}.log*
#appender.file.strategy.action.condition.nested_condition.type = IfAny
#appender.file.strategy.action.condition.nested_condition.lastModify.type = IfLastModified
#appender.file.strategy.action.condition.nested_condition.lastModify.age = ${file_ttl}
#appender.file.strategy.action.condition.nested_condition.fileCount.type = IfAccumulatedFileCount
#appender.file.strategy.action.condition.nested_condition.fileCount.exceeds = ${file_count}