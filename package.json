{"name": "typescript-action", "version": "0.0.0", "private": true, "description": "TypeScript template action", "main": "lib/main.js", "scripts": {"build": "tsc", "format": "prettier --write **/*.ts", "format-check": "prettier --check **/*.ts", "lint": "eslint src/**/*.ts", "pack": "ncc build", "test": "jest", "all": "npm run build && npm run format && npm run lint && npm run pack && npm test", "release": "ncc build -o dist src/main.ts"}, "repository": {"type": "git", "url": "git+https://github.com/actions/typescript-action.git"}, "keywords": ["actions", "node", "setup"], "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"@actions/core": "^1.2.2", "@actions/github": "^2.1.0", "npm": "^7.7.5"}, "devDependencies": {"@types/jest": "^24.0.23", "@types/node": "^12.7.12", "@typescript-eslint/parser": "^2.8.0", "@zeit/ncc": "^0.20.5", "eslint": "^5.16.0", "eslint-plugin-github": "^2.0.0", "eslint-plugin-jest": "^22.21.0", "jest": "^26.2.2", "jest-circus": "^26.2.2", "js-yaml": "^3.13.1", "prettier": "^1.19.1", "ts-jest": "^26.1.4", "typescript": "^3.6.4"}}