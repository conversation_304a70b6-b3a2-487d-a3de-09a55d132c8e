// 修复后的SFTP连接池实现
// 路径: seatunnel-connectors-v2/connector-file/connector-file-sftp/src/main/java/org/apache/seatunnel/connectors/seatunnel/file/sftp/system/SFTPConnectionPool.java

package org.apache.seatunnel.connectors.seatunnel.file.sftp.system;

import org.apache.hadoop.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;

import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

public class SFTPConnectionPool {

    public static final Logger LOG = LoggerFactory.getLogger(SFTPFileSystem.class);
    
    // 使用线程安全的数据结构
    private volatile int maxConnection;
    private final AtomicInteger liveConnectionCount = new AtomicInteger(0);
    private final ConcurrentHashMap<ConnectionInfo, HashSet<ChannelSftp>> idleConnections = 
            new ConcurrentHashMap<>();
    private final ConcurrentHashMap<ChannelSftp, ConnectionInfo> con2infoMap = 
            new ConcurrentHashMap<>();

    SFTPConnectionPool(int maxConnection, int liveConnectionCount) {
        this.maxConnection = maxConnection;
        this.liveConnectionCount.set(liveConnectionCount);
    }

    /**
     * 修复后的连接获取方法
     */
    synchronized ChannelSftp getFromPool(ConnectionInfo info) throws IOException {
        HashSet<ChannelSftp> cons = idleConnections.get(info);
        ChannelSftp channel = null;

        if (cons != null && cons.size() > 0) {
            Iterator<ChannelSftp> it = cons.iterator();
            if (it.hasNext()) {
                channel = it.next();
                // 修复：只移除单个channel，而不是整个ConnectionInfo
                it.remove();
                
                // 如果这是最后一个连接，移除空的Set
                if (cons.isEmpty()) {
                    idleConnections.remove(info);
                }
                
                // 验证连接是否仍然有效
                if (channel != null && !channel.isConnected()) {
                    LOG.warn("Retrieved disconnected channel from pool, removing it");
                    con2infoMap.remove(channel);
                    liveConnectionCount.decrementAndGet();
                    channel = null;
                }
                
                return channel;
            }
        }
        return null;
    }

    /**
     * 改进的连接归还方法
     */
    synchronized void returnToPool(ChannelSftp channel) {
        if (channel == null || !channel.isConnected()) {
            LOG.warn("Attempting to return null or disconnected channel to pool");
            if (channel != null) {
                con2infoMap.remove(channel);
                liveConnectionCount.decrementAndGet();
            }
            return;
        }
        
        ConnectionInfo info = con2infoMap.get(channel);
        if (info == null) {
            LOG.warn("Channel not found in connection mapping, closing it");
            try {
                Session session = channel.getSession();
                channel.disconnect();
                session.disconnect();
            } catch (JSchException e) {
                LOG.error("Error closing orphaned channel", e);
            }
            return;
        }
        
        HashSet<ChannelSftp> cons = idleConnections.computeIfAbsent(info, k -> new HashSet<>());
        cons.add(channel);
    }

    /**
     * 改进的连接创建方法
     */
    public ChannelSftp connect(String host, int port, String user, String password, String keyFile)
            throws IOException {
        // 首先尝试从池中获取连接
        ConnectionInfo info = new ConnectionInfo(host, port, user);
        ChannelSftp channel = getFromPool(info);

        if (channel != null && channel.isConnected()) {
            return channel;
        }

        // 检查连接数限制
        if (liveConnectionCount.get() >= maxConnection) {
            LOG.warn("Maximum connection limit reached: {}, current: {}", 
                    maxConnection, liveConnectionCount.get());
            // 可以选择等待或抛出异常
            throw new IOException("SFTP connection pool exhausted. Max connections: " + maxConnection);
        }

        // 创建新连接
        JSch jsch = new JSch();
        Session session = null;
        try {
            if (user == null || user.length() == 0) {
                user = System.getProperty("user.name");
            }

            if (password == null) {
                password = "";
            }

            if (keyFile != null && keyFile.length() > 0) {
                jsch.addIdentity(keyFile);
            }

            if (port <= 0) {
                session = jsch.getSession(user, host);
            } else {
                session = jsch.getSession(user, host, port);
            }

            session.setPassword(password);

            java.util.Properties config = new java.util.Properties();
            config.put("StrictHostKeyChecking", "no");
            // 添加连接超时设置
            config.put("ConnectTimeout", "30000");
            session.setConfig(config);

            session.connect();
            channel = (ChannelSftp) session.openChannel("sftp");
            channel.connect();

            // 原子性地更新连接计数和映射
            con2infoMap.put(channel, info);
            liveConnectionCount.incrementAndGet();

            LOG.debug("Created new SFTP connection. Live connections: {}", liveConnectionCount.get());
            return channel;

        } catch (JSchException e) {
            if (session != null && session.isConnected()) {
                session.disconnect();
            }
            throw new IOException("Failed to create SFTP connection: " + StringUtils.stringifyException(e));
        }
    }

    /**
     * 改进的连接断开方法
     */
    void disconnect(ChannelSftp channel) throws IOException {
        if (channel == null) {
            return;
        }

        boolean shouldClose = false;
        synchronized (this) {
            if (liveConnectionCount.get() > maxConnection) {
                con2infoMap.remove(channel);
                liveConnectionCount.decrementAndGet();
                shouldClose = true;
            }
        }

        if (shouldClose) {
            if (channel.isConnected()) {
                try {
                    Session session = channel.getSession();
                    channel.disconnect();
                    session.disconnect();
                    LOG.debug("Closed excess SFTP connection. Live connections: {}", liveConnectionCount.get());
                } catch (JSchException e) {
                    throw new IOException(StringUtils.stringifyException(e));
                }
            }
        } else {
            returnToPool(channel);
        }
    }

    /** 
     * 改进的关闭方法
     */
    synchronized void shutdown() {
        if (this.con2infoMap.isEmpty()) {
            return; // already shutdown
        }
        
        LOG.info("Shutting down SFTP connection pool. Active connections: {}", con2infoMap.size());

        this.maxConnection = 0;
        Set<ChannelSftp> cons = new HashSet<>(con2infoMap.keySet());
        
        // 关闭所有连接
        for (ChannelSftp con : cons) {
            try {
                if (con.isConnected()) {
                    Session session = con.getSession();
                    con.disconnect();
                    session.disconnect();
                }
            } catch (Exception e) {
                ConnectionInfo info = con2infoMap.get(con);
                LOG.error("Error closing connection to " + (info != null ? info.getHost() : "unknown"), e);
            }
        }
        
        // 清理所有数据结构
        this.idleConnections.clear();
        this.con2infoMap.clear();
        this.liveConnectionCount.set(0);
    }

    // Getter方法保持不变
    public synchronized int getMaxConnection() {
        return maxConnection;
    }

    public synchronized void setMaxConnection(int maxConn) {
        this.maxConnection = maxConn;
    }

    public int getIdleCount() {
        return this.idleConnections.size();
    }

    public int getLiveConnCount() {
        return this.liveConnectionCount.get();
    }

    public int getConnPoolSize() {
        return this.con2infoMap.size();
    }

    // ConnectionInfo类保持不变
    static class ConnectionInfo {
        private String host = "";
        private int port;
        private String user = "";

        ConnectionInfo(String hst, int prt, String usr) {
            this.host = hst;
            this.port = prt;
            this.user = usr;
        }

        public String getHost() { return host; }
        public void setHost(String hst) { this.host = hst; }
        public int getPort() { return port; }
        public void setPort(int prt) { this.port = prt; }
        public String getUser() { return user; }
        public void setUser(String usr) { this.user = usr; }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj instanceof ConnectionInfo) {
                ConnectionInfo con = (ConnectionInfo) obj;
                boolean ret = true;
                if (this.host == null || !this.host.equalsIgnoreCase(con.host)) ret = false;
                if (this.port >= 0 && this.port != con.port) ret = false;
                if (this.user == null || !this.user.equalsIgnoreCase(con.user)) ret = false;
                return ret;
            }
            return false;
        }

        @Override
        public int hashCode() {
            int hashCode = 0;
            if (host != null) hashCode += host.hashCode();
            hashCode += port;
            if (user != null) hashCode += user.hashCode();
            return hashCode;
        }
    }
}
