env {
  # 降低并发度以避免竞争条件
  execution.parallelism = 1
  parallelism = 1
  job.mode = "STREAMING"
  checkpoint.interval = 10000
  
  # 限制读取速度，避免过快的数据流
  read_limit.bytes_per_second = 1000000
  read_limit.rows_per_second = 100
}

source {
  Jdbc {
    url = "***********************************************"
    driver = "com.mysql.cj.jdbc.Driver"
    username = "your_username"
    password = "your_password"
    
    # 使用单表查询避免多表并发
    query = "select * from your_table"
    
    # 如果需要分区，使用较小的分区数
    # partition_column = "id"
    # partition_num = 1
    
    plugin_output = "mysql_source"
  }
}

transform {
  # 可以添加必要的数据转换
}

sink {
  SftpFile {
    host = "your-sftp-host"
    port = 22
    user = "your_username"
    password = "your_password"
    
    # 使用时间戳避免文件名冲突
    path = "/tmp/seatunnel/mysql_sync/${now}"
    plugin_input = "mysql_source"
    
    file_format_type = "text"
    row_delimiter = "\n"
    field_delimiter = ","
    
    # 启用事务确保数据一致性
    is_enable_transaction = true
    
    # 文件名包含时间戳和随机数
    file_name_expression = "${transactionId}_${now}_${random}"
    filename_time_format = "yyyy-MM-dd-HH-mm-ss"
    
    # 数据保存模式
    schema_save_mode = "CREATE_SCHEMA_WHEN_NOT_EXIST"
    data_save_mode = "APPEND_DATA"
    
    # SFTP连接配置
    connection_check_timeout_sec = 30
    max_retries = 3
  }
}
