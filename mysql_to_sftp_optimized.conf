env {
  # 核心配置：降低并发度避免连接池竞争
  execution.parallelism = 1
  parallelism = 1
  job.mode = "STREAMING"
  checkpoint.interval = 10000
  
  # 限制数据流速度，减少SFTP压力
  read_limit.bytes_per_second = 500000
  read_limit.rows_per_second = 50
}

source {
  Jdbc {
    url = "***********************************************"
    driver = "com.mysql.cj.jdbc.Driver"
    username = "your_username"
    password = "your_password"
    
    # 使用单表查询，避免多表并发问题
    query = "select * from your_table"
    
    # 如果必须使用分区，使用最小分区数
    # partition_column = "id"
    # partition_num = 1
    
    # 连接池配置
    connection_check_timeout_sec = 30
    max_retries = 3
    
    plugin_output = "mysql_source"
  }
}

transform {
  # 可以添加必要的数据转换
}

sink {
  SftpFile {
    host = "your-sftp-host"
    port = 22
    user = "your_username"
    password = "your_password"
    
    # 使用时间戳避免文件名冲突
    path = "/tmp/seatunnel/mysql_sync/${now}"
    plugin_input = "mysql_source"
    
    file_format_type = "text"
    row_delimiter = "\n"
    field_delimiter = ","
    
    # 关键配置：启用事务确保数据一致性
    is_enable_transaction = true
    
    # 文件名包含时间戳和随机数避免冲突
    file_name_expression = "${transactionId}_${now}_${random}"
    filename_time_format = "yyyy-MM-dd-HH-mm-ss"
    
    # 数据保存模式
    schema_save_mode = "CREATE_SCHEMA_WHEN_NOT_EXIST"
    data_save_mode = "APPEND_DATA"
    
    # SFTP连接优化配置
    connection_check_timeout_sec = 30
    max_retries = 3
    
    # Hadoop配置优化SFTP连接池
    hadoop_conf = {
      "fs.sftp.connection.max" = "3"  # 限制最大连接数
      "fs.sftp.impl" = "org.apache.seatunnel.connectors.seatunnel.file.sftp.system.SFTPFileSystem"
    }
  }
}

# 如果需要处理多表，建议分别配置多个作业
# 而不是在单个作业中并发处理多表
