env {
  # 串行处理，避免SFTP服务器过载
  execution.parallelism = 1
  parallelism = 1
  job.mode = "BATCH"
  checkpoint.interval = 30000
}

source {
  Jdbc {
    url = "***********************************************"
    driver = "com.mysql.cj.jdbc.Driver"
    username = "your_username"
    password = "your_password"
    
    query = "select * from your_test_table"
    fetch_size = 5000
    
    connection_check_timeout_sec = 60
    max_retries = 3
    
    plugin_output = "mysql_source"
  }
}

sink {
  SftpFile {
    host = "your-sftp-host"
    port = 22
    user = "your_username"
    password = "your_password"
    
    path = "/tmp/seatunnel/sequential_${now}"
    plugin_input = "mysql_source"
    
    file_format_type = "text"
    row_delimiter = "\n"
    field_delimiter = ","
    
    is_enable_transaction = true
    file_name_expression = "sequential_${transactionId}_${now}"
    filename_time_format = "yyyy-MM-dd-HH-mm-ss"
    
    schema_save_mode = "CREATE_SCHEMA_WHEN_NOT_EXIST"
    data_save_mode = "APPEND_DATA"
    
    # 大批次处理，减少网络调用
    batch_size = 10000
    single_file_mode = true
    
    connection_check_timeout_sec = 60
    max_retries = 5
    
    hadoop_conf = {
      "fs.sftp.connection.max" = "1"      # 只使用1个连接
      "fs.sftp.impl" = "org.apache.seatunnel.connectors.seatunnel.file.sftp.system.SFTPFileSystem"
      "fs.sftp.buffer.size" = "131072"    # 128KB大缓冲区
    }
  }
}
