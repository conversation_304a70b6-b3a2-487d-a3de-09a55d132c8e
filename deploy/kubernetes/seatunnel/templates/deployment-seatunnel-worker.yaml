#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "seatunnel.fullname" . }}-worker
  labels:
    {{- include "seatunnel.worker.labels" . | nindent 4 }}
spec:
  {{- if .Values.worker.strategy }}
  strategy:
    {{- toYaml .Values.worker.strategy | nindent 4 }}
  {{- end }}
  replicas: {{ .Values.worker.replicas }}
  selector:
    matchLabels:
      {{- include "seatunnel.worker.labels" . | nindent 6 }}
  template:
    metadata:
      {{- if .Values.worker.annotations }}
      annotations:
        {{- toYaml .Values.worker.annotations | nindent 8 }}
      {{- end }}
      labels:
        {{- include "seatunnel.worker.labels" . | nindent 8 }}
    spec:
      serviceAccountName: {{ template "seatunnel.fullname" . }}
      {{- if .Values.worker.affinity }}
      affinity:
        {{- toYaml .Values.worker.affinity | nindent 8 }}
      {{- end }}
      {{- if .Values.worker.nodeSelector }}
      nodeSelector:
        {{- toYaml .Values.worker.nodeSelector | nindent 8 }}
      {{- end }}
      {{- if .Values.worker.tolerations }}
      tolerations:
        {{- toYaml .Values.worker.tolerations | nindent 8 }}
      {{- end }}
      {{- if .Values.image.pullSecret }}
      imagePullSecrets:
        - name: {{ .Values.image.pullSecret }}
      {{- end }}
      containers:
        - name: {{ include "seatunnel.fullname" . }}-worker
          image: {{ include "seatunnel.image.fullname.worker" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - containerPort: 5801
              name: "hazelcast-port"
          {{- if .Values.worker.command }}
          command: {{ .Values.worker.command }}
          {{- else }}
          command: ["/bin/sh","-c","/opt/seatunnel/bin/seatunnel-cluster.sh -r worker"]
          {{- end }}
          {{- if .Values.worker.resources }}
          resources:
            {{- toYaml .Values.worker.resources | nindent 12 }}
          {{- end }}
          {{- if .Values.worker.livenessProbe.enabled }}
          livenessProbe:
            {{- toYaml .Values.worker.livenessProbe | nindent 12 }}
          {{- end }}
          {{- if .Values.env }}
          env:
            {{- toYaml .Values.env | nindent 12 }}
          {{- end }}
          volumeMounts:
            # config mount
            {{- range $path, $_ := .Files.Glob "conf/*" }}
            - name: seatunnel-configs
              mountPath: /opt/seatunnel/config/{{ base $path }}
              subPath: {{ base $path }}
            {{- end }}
      volumes:
        - name: seatunnel-configs
          configMap:
            name: {{ include "seatunnel.fullname" . }}-configs