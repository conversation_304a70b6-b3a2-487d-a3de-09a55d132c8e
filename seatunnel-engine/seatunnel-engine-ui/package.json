{"name": "seatunnel-engine-ui", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "start-server-and-test preview http://localhost:4173 'cypress run --e2e'", "test:e2e:dev": "start-server-and-test 'vite dev --port 4173' http://localhost:4173 'cypress open --e2e'", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@antv/x6": "^2.18.1", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-vue-shape": "^2.1.2", "@vicons/ionicons5": "^0.12.0", "autoprefixer": "^10.4.20", "axios": "^1.7.7", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "naive-ui": "^2.39.0", "nprogress": "^0.2.0", "pinia": "^2.1.7", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "vue": "^3.4.29", "vue-i18n": "^10.0.1", "vue-router": "^4.3.3"}, "devDependencies": {"@pinia/testing": "^0.1.5", "@rushstack/eslint-patch": "^1.8.0", "@tsconfig/node20": "^20.1.4", "@types/jsdom": "^21.1.7", "@types/node": "^20.14.5", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "^4.0.0", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.5.1", "cypress": "^13.12.0", "eslint": "^8.57.0", "eslint-plugin-cypress": "^3.3.0", "eslint-plugin-vue": "^9.23.0", "jsdom": "^24.1.0", "npm-run-all2": "^6.2.0", "prettier": "^3.2.5", "sass-embedded": "^1.78.0", "start-server-and-test": "^2.0.4", "typescript": "~5.4.0", "vite": "^5.3.1", "vite-plugin-vue-devtools": "^7.3.1", "vitest": "^1.5.3", "vue-tsc": "^2.0.21"}}