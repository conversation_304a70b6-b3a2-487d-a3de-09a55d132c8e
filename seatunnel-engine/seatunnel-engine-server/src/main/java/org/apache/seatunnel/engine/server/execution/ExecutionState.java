/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.engine.server.execution;

import java.io.Serializable;

/**
 * An enumeration of all states that a task can be in during its execution. Tasks usually start in
 * the state {@code CREATED} and switch states according to this diagram:
 *
 * <pre>{@code
 * INITIALIZING -> CREATED  -> SCHEDULED -> DEPLOYING  -> RUNNING -> FINISHED
 *                   |            |          |              |
 *                   |            |    +-----+--------------+
 *                   |            V    V
 *                   |         CANCELLING -----+----> CANCELED
 *                   |                         |
 *                   +-------------------------+
 *
 *                                        ... -> FAILED
 *
 * }</pre>
 *
 * <p>It is possible to enter the {@code RECONCILING} state from {@code CREATED} state if job
 * manager fail over, and the {@code RECONCILING} state can switch into any existing task state.
 *
 * <p>It is possible to enter the {@code FAILED} state from any other state.
 *
 * <p>The states {@code FINISHED}, {@code CANCELED}, and {@code FAILED} are considered terminal
 * states.
 */
public enum ExecutionState implements Serializable {
    CREATED,

    DEPLOYING,

    RUNNING,

    /**
     * This state marks "successfully completed". It can only be reached when a program reaches the
     * "end of its input". The "end of input" can be reached when consuming a bounded input (fix set
     * of files, bounded query, etc) or when stopping a program (not cancelling!) which make the
     * input look like it reached its end at a specific point.
     */
    FINISHED,

    CANCELING,

    CANCELED,

    FAILING,

    FAILED,

    /** Restoring last possible valid state of the task if it has it. */
    INITIALIZING;

    public boolean isEndState() {
        return this == FINISHED || this == CANCELED || this == FAILED;
    }
}
