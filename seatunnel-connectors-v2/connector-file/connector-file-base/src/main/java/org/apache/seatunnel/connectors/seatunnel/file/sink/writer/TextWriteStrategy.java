/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.file.sink.writer;

import org.apache.seatunnel.api.serialization.SerializationSchema;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.common.exception.CommonError;
import org.apache.seatunnel.common.exception.CommonErrorCodeDeprecated;
import org.apache.seatunnel.common.utils.DateTimeUtils;
import org.apache.seatunnel.common.utils.DateUtils;
import org.apache.seatunnel.common.utils.EncodingUtils;
import org.apache.seatunnel.common.utils.TimeUtils;
import org.apache.seatunnel.connectors.seatunnel.file.config.FileFormat;
import org.apache.seatunnel.connectors.seatunnel.file.exception.FileConnectorException;
import org.apache.seatunnel.connectors.seatunnel.file.sink.config.FileSinkConfig;
import org.apache.seatunnel.format.text.TextSerializationSchema;

import org.apache.hadoop.fs.FSDataOutputStream;

import io.airlift.compress.lzo.LzopCodec;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
public class TextWriteStrategy extends AbstractWriteStrategy<FSDataOutputStream> {
    private final LinkedHashMap<String, FSDataOutputStream> beingWrittenOutputStream;
    private final Map<String, Boolean> isFirstWrite;
    private final String fieldDelimiter;
    private final String rowDelimiter;
    private final DateUtils.Formatter dateFormat;
    private final DateTimeUtils.Formatter dateTimeFormat;
    private final TimeUtils.Formatter timeFormat;
    private final FileFormat fileFormat;
    private final Boolean enableHeaderWriter;
    private final Charset charset;

    // 写入超时执行器
    private static final ExecutorService WRITE_EXECUTOR = Executors.newCachedThreadPool(r -> {
        Thread t = new Thread(r, "sftp-write-timeout-thread");
        t.setDaemon(true);
        return t;
    });
    private static final long WRITE_TIMEOUT_SECONDS = 10; // 10秒写入超时
    private SerializationSchema serializationSchema;

    public TextWriteStrategy(FileSinkConfig fileSinkConfig) {
        super(fileSinkConfig);
        this.beingWrittenOutputStream = new LinkedHashMap<>();
        this.isFirstWrite = new HashMap<>();
        this.fieldDelimiter = fileSinkConfig.getFieldDelimiter();
        this.rowDelimiter = fileSinkConfig.getRowDelimiter();
        this.dateFormat = fileSinkConfig.getDateFormat();
        this.dateTimeFormat = fileSinkConfig.getDatetimeFormat();
        this.timeFormat = fileSinkConfig.getTimeFormat();
        this.fileFormat = fileSinkConfig.getFileFormat();
        this.enableHeaderWriter = fileSinkConfig.getEnableHeaderWriter();
        this.charset = EncodingUtils.tryParseCharset(fileSinkConfig.getEncoding());
    }

    @Override
    public void setCatalogTable(CatalogTable catalogTable) {
        super.setCatalogTable(catalogTable);
        this.serializationSchema =
                TextSerializationSchema.builder()
                        .seaTunnelRowType(
                                buildSchemaWithRowType(
                                        catalogTable.getSeaTunnelRowType(), sinkColumnsIndexInRow))
                        .delimiter(fieldDelimiter)
                        .dateFormatter(dateFormat)
                        .dateTimeFormatter(dateTimeFormat)
                        .timeFormatter(timeFormat)
                        .charset(charset)
                        .build();
    }

    @Override
    public void write(@NonNull SeaTunnelRow seaTunnelRow) {
        super.write(seaTunnelRow);
        String filePath = getOrCreateFilePathBeingWritten(seaTunnelRow);
        FSDataOutputStream fsDataOutputStream = getOrCreateOutputStream(filePath);

        // 使用超时机制执行写入操作
        CompletableFuture<Void> writeTask = CompletableFuture.runAsync(() -> {
            try {
                if (isFirstWrite.get(filePath)) {
                    isFirstWrite.put(filePath, false);
                } else {
                    fsDataOutputStream.write(rowDelimiter.getBytes(charset));
                }
                fsDataOutputStream.write(
                        serializationSchema.serialize(
                                seaTunnelRow.copy(
                                        sinkColumnsIndexInRow.stream()
                                                .mapToInt(Integer::intValue)
                                                .toArray())));
                // 强制刷新，确保数据写入
                fsDataOutputStream.hflush();
            } catch (IOException e) {
                throw new RuntimeException("Write operation failed", e);
            }
        }, WRITE_EXECUTOR);

        try {
            // 等待写入完成，最多等待30秒
            writeTask.get(WRITE_TIMEOUT_SECONDS, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            writeTask.cancel(true);
            log.error("Write operation timed out after {} seconds for file: {}", WRITE_TIMEOUT_SECONDS, filePath);
            throw CommonError.fileOperationFailed("TextFile", "write timeout", filePath, e);
        } catch (Exception e) {
            writeTask.cancel(true);
            Throwable cause = e.getCause();
            if (cause instanceof RuntimeException && cause.getCause() instanceof IOException) {
                throw CommonError.fileOperationFailed("TextFile", "write", filePath, (IOException) cause.getCause());
            }
            throw CommonError.fileOperationFailed("TextFile", "write", filePath, new IOException(e));
        }
    }

    @Override
    public void finishAndCloseFile() {
        beingWrittenOutputStream.forEach(
                (key, value) -> {
                    try {
                        value.flush();
                    } catch (IOException e) {
                        throw new FileConnectorException(
                                CommonErrorCodeDeprecated.FLUSH_DATA_FAILED,
                                String.format("Flush data to this file [%s] failed", key),
                                e);
                    } finally {
                        try {
                            value.close();
                        } catch (IOException e) {
                            log.error("error when close output stream {}", key, e);
                        }
                    }
                    needMoveFiles.put(key, getTargetLocation(key));
                });
        beingWrittenOutputStream.clear();
        isFirstWrite.clear();
    }

    @Override
    public FSDataOutputStream getOrCreateOutputStream(@NonNull String filePath) {
        FSDataOutputStream fsDataOutputStream = beingWrittenOutputStream.get(filePath);
        if (fsDataOutputStream == null) {
            try {
                switch (compressFormat) {
                    case LZO:
                        LzopCodec lzo = new LzopCodec();
                        OutputStream out =
                                lzo.createOutputStream(
                                        hadoopFileSystemProxy.getOutputStream(filePath));
                        fsDataOutputStream = new FSDataOutputStream(out, null);
                        enableWriteHeader(fsDataOutputStream);
                        break;
                    case NONE:
                        fsDataOutputStream = hadoopFileSystemProxy.getOutputStream(filePath);
                        enableWriteHeader(fsDataOutputStream);
                        break;
                    default:
                        log.warn(
                                "Text file does not support this compress type: {}",
                                compressFormat.getCompressCodec());
                        fsDataOutputStream = hadoopFileSystemProxy.getOutputStream(filePath);
                        enableWriteHeader(fsDataOutputStream);
                        break;
                }
                beingWrittenOutputStream.put(filePath, fsDataOutputStream);
                isFirstWrite.put(filePath, true);
            } catch (IOException e) {
                throw CommonError.fileOperationFailed("TextFile", "open", filePath, e);
            }
        }
        return fsDataOutputStream;
    }

    private void enableWriteHeader(FSDataOutputStream fsDataOutputStream) throws IOException {
        if (enableHeaderWriter) {
            fsDataOutputStream.write(
                    String.join(fieldDelimiter, seaTunnelRowType.getFieldNames()).getBytes());
            fsDataOutputStream.write(rowDelimiter.getBytes());
        }
    }

    @Override
    public void close() throws IOException {
        try {
            super.close();
        } finally {
            // 关闭写入超时执行器
            if (!WRITE_EXECUTOR.isShutdown()) {
                WRITE_EXECUTOR.shutdown();
                try {
                    if (!WRITE_EXECUTOR.awaitTermination(5, TimeUnit.SECONDS)) {
                        WRITE_EXECUTOR.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    WRITE_EXECUTOR.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
        }
    }
}
