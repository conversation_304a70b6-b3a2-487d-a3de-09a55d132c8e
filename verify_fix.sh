#!/bin/bash

# 验证SFTP连接池修复的脚本

echo "=== 验证SFTP连接池修复 ==="

SFTP_FILE="seatunnel-connectors-v2/connector-file/connector-file-sftp/src/main/java/org/apache/seatunnel/connectors/seatunnel/file/sftp/system/SFTPConnectionPool.java"

if [ ! -f "$SFTP_FILE" ]; then
    echo "错误: 找不到SFTPConnectionPool.java文件"
    exit 1
fi

echo "检查文件: $SFTP_FILE"
echo ""

# 检查关键修复
echo "1. 检查关键bug修复..."
if grep -n "it.remove()" "$SFTP_FILE"; then
    echo "   ✓ 找到关键修复: it.remove() 而不是 idleConnections.remove(info)"
else
    echo "   ✗ 未找到关键修复"
    exit 1
fi

echo ""
echo "2. 检查线程安全改进..."
if grep -n "AtomicInteger" "$SFTP_FILE"; then
    echo "   ✓ 找到AtomicInteger改进"
else
    echo "   ✗ 未找到AtomicInteger改进"
fi

if grep -n "ConcurrentHashMap" "$SFTP_FILE"; then
    echo "   ✓ 找到ConcurrentHashMap改进"
else
    echo "   ✗ 未找到ConcurrentHashMap改进"
fi

echo ""
echo "3. 检查连接验证..."
if grep -n "channel.isConnected()" "$SFTP_FILE"; then
    echo "   ✓ 找到连接有效性验证"
else
    echo "   ✗ 未找到连接有效性验证"
fi

echo ""
echo "4. 检查错误处理改进..."
if grep -n "ConnectTimeout" "$SFTP_FILE"; then
    echo "   ✓ 找到连接超时配置"
else
    echo "   ✗ 未找到连接超时配置"
fi

echo ""
echo "5. 显示修复前后的关键差异..."
echo "修复前的问题代码:"
echo "   idleConnections.remove(info);  // 错误：移除了整个ConnectionInfo"
echo ""
echo "修复后的正确代码:"
grep -A 5 -B 2 "it.remove()" "$SFTP_FILE"

echo ""
echo "=== 验证完成 ==="
echo ""
echo "如果所有检查都通过，说明修复已正确应用。"
echo "现在可以重新编译并测试SeaTunnel。"
