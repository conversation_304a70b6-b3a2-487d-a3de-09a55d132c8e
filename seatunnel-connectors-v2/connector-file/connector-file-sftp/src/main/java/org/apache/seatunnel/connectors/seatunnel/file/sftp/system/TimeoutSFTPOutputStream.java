/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.file.sftp.system;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.OutputStream;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/** 带超时机制的SFTP输出流包装器 解决SFTP网络写入阻塞问题 */
@Slf4j
public class TimeoutSFTPOutputStream extends OutputStream {

    private final OutputStream delegate;
    private final long timeoutSeconds;
    private static final ExecutorService EXECUTOR =
            Executors.newCachedThreadPool(
                    r -> {
                        Thread t = new Thread(r, "sftp-timeout-writer");
                        t.setDaemon(true);
                        return t;
                    });

    private volatile boolean closed = false;

    public TimeoutSFTPOutputStream(OutputStream delegate, long timeoutSeconds) {
        this.delegate = delegate;
        this.timeoutSeconds = timeoutSeconds;
    }

    @Override
    public void write(int b) throws IOException {
        if (closed) {
            throw new IOException("Stream is closed");
        }

        executeWithTimeout(
                () -> {
                    try {
                        delegate.write(b);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                },
                "write single byte");
    }

    @Override
    public void write(byte[] b) throws IOException {
        if (closed) {
            throw new IOException("Stream is closed");
        }

        executeWithTimeout(
                () -> {
                    try {
                        delegate.write(b);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                },
                "write byte array");
    }

    @Override
    public void write(byte[] b, int off, int len) throws IOException {
        if (closed) {
            throw new IOException("Stream is closed");
        }

        executeWithTimeout(
                () -> {
                    try {
                        delegate.write(b, off, len);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                },
                "write byte array with offset");
    }

    @Override
    public void flush() throws IOException {
        if (closed) {
            throw new IOException("Stream is closed");
        }

        executeWithTimeout(
                () -> {
                    try {
                        delegate.flush();
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                },
                "flush");
    }

    @Override
    public void close() throws IOException {
        if (closed) {
            return;
        }

        closed = true;
        executeWithTimeout(
                () -> {
                    try {
                        delegate.close();
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                },
                "close");
    }

    private void executeWithTimeout(Runnable operation, String operationName) throws IOException {
        CompletableFuture<Void> future = CompletableFuture.runAsync(operation, EXECUTOR);

        try {
            future.get(timeoutSeconds, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            future.cancel(true);
            log.error(
                    "SFTP {} operation timed out after {} seconds", operationName, timeoutSeconds);
            throw new IOException(
                    "SFTP "
                            + operationName
                            + " operation timed out after "
                            + timeoutSeconds
                            + " seconds",
                    e);
        } catch (Exception e) {
            future.cancel(true);
            Throwable cause = e.getCause();
            if (cause instanceof RuntimeException && cause.getCause() instanceof IOException) {
                throw (IOException) cause.getCause();
            }
            throw new IOException("SFTP " + operationName + " operation failed", e);
        }
    }

    public static void shutdown() {
        if (!EXECUTOR.isShutdown()) {
            EXECUTOR.shutdown();
            try {
                if (!EXECUTOR.awaitTermination(5, TimeUnit.SECONDS)) {
                    EXECUTOR.shutdownNow();
                }
            } catch (InterruptedException e) {
                EXECUTOR.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
