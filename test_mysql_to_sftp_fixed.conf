env {
  # 降低并发度，避免SFTP网络阻塞
  execution.parallelism = 1
  parallelism = 1
  job.mode = "BATCH"
  checkpoint.interval = 10000

  # 增加任务超时时间
  job.timeout = 3600000
}

source {
  Jdbc {
    url = "***********************************************"
    driver = "com.mysql.cj.jdbc.Driver"
    username = "your_username"
    password = "your_password"
    
    # 测试查询
    query = "select * from your_test_table limit 1000"
    
    # 连接池配置
    connection_check_timeout_sec = 30
    max_retries = 3
    
    plugin_output = "mysql_source"
  }
}

sink {
  SftpFile {
    host = "your-sftp-host"
    port = 22
    user = "your_username"
    password = "your_password"
    
    # 测试路径
    path = "/tmp/seatunnel/test_fixed/${now}"
    plugin_input = "mysql_source"
    
    file_format_type = "text"
    row_delimiter = "\n"
    field_delimiter = ","
    
    # 启用事务
    is_enable_transaction = true
    
    # 文件名配置
    file_name_expression = "test_${transactionId}_${now}"
    filename_time_format = "yyyy-MM-dd-HH-mm-ss"
    
    # 数据保存模式
    schema_save_mode = "CREATE_SCHEMA_WHEN_NOT_EXIST"
    data_save_mode = "APPEND_DATA"
    
    # SFTP连接优化配置
    connection_check_timeout_sec = 60
    max_retries = 5

    # 减少批次大小，避免长时间阻塞
    batch_size = 1000

    # Hadoop配置优化SFTP连接池和网络
    hadoop_conf = {
      "fs.sftp.connection.max" = "3"  # 减少连接数，避免网络拥塞
      "fs.sftp.impl" = "org.apache.seatunnel.connectors.seatunnel.file.sftp.system.SFTPFileSystem"
      "fs.sftp.timeout" = "60000"     # 60秒超时
      "fs.sftp.buffer.size" = "32768" # 32KB缓冲区，减少网络调用
    }
  }
}
