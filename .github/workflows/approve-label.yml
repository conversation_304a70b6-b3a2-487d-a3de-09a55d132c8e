# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

name: "Label when approved workflow run"
on:
  workflow_run:
    workflows: [Label-when-reviewed]
    types: [requested]
permissions:
  # All other permissions are set to none
  checks: write
  contents: read
  pull-requests: write
jobs:
  label-when-approved:
    name: "Label when approved"
    runs-on: ubuntu-latest
    outputs:
      isApprovedByCommiters: ${{ steps.label-when-approved-by-commiters.outputs.isApproved }}
      isApprovedByAnyone: ${{ steps.label-when-approved-by-anyone.outputs.isApproved }}
    steps:
      - name: "Checkout ${{ github.ref }} ( ${{ github.sha }} )"
        uses: actions/checkout@v2
        with:
          persist-credentials: false
          submodules: recursive
      - name: "Get information about the original trigger of the run"
        uses: ./.github/actions/get-workflow-origin
        id: source-run-info
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          sourceRunId: ${{ github.event.workflow_run.id }}
      - name: Label when approved by commiters
        uses: ./.github/actions/label-when-approved-action
        id: label-when-approved-by-commiters
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          label: 'approved'
          require_committers_approval: 'true'
          remove_label_when_approval_missing: 'true'
          pullRequestNumber: ${{ steps.source-run-info.outputs.pullRequestNumber }}
      - name: Label when approved by anyone
        uses: ./.github/actions/label-when-approved-action
        id: label-when-approved-by-anyone
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          label: 'reviewed'
          pullRequestNumber: ${{ steps.source-run-info.outputs.pullRequestNumber }}
          remove_label_when_approval_missing: 'true'
