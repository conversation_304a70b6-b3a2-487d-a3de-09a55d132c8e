{"ignorePatterns": [{"pattern": "^http://localhost"}, {"pattern": "^https://mvnrepository.com"}, {"pattern": "^https://www.qutoutiao.net"}, {"pattern": "^https://img.shields.io"}, {"pattern": "^https://json.org/"}, {"pattern": "^/docs/category"}, {"pattern": "^https://opencollective.com"}, {"pattern": "^https://twitter.com/ASFSeaTunnel"}, {"pattern": "^https://github.com/apache/seatunnel/commit/"}], "timeout": "10s", "retryOn429": true, "retryCount": 10, "fallbackRetryDelay": "1000s", "aliveStatusCodes": [0, 200, 401, 403]}