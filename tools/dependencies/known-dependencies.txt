commons-codec-1.13.jar
commons-collections4-4.4.jar
commons-compress-1.20.jar
commons-io-2.11.0.jar
commons-lang3-3.8.jar
commons-csv-1.10.0.jar
config-1.3.3.jar
disruptor-3.4.4.jar
guava-27.0-jre.jar
hazelcast-5.1.jar
httpclient-4.5.13.jar
httpcore-4.4.16.jar
jackson-annotations-2.13.3.jar
jackson-core-2.13.3.jar
jackson-databind-2.13.3.jar
jackson-dataformat-properties-2.13.3.jar
jackson-datatype-jsr310-2.13.3.jar
jcl-over-slf4j-1.7.36.jar
jcommander-1.81.jar
log4j-api-2.17.1.jar
log4j-core-2.17.1.jar
log4j-slf4j-impl-2.17.1.jar
log4j-1.2-api-2.17.1.jar
protostuff-api-1.8.0.jar
protostuff-collectionschema-1.8.0.jar
protostuff-core-1.8.0.jar
protostuff-runtime-1.8.0.jar
scala-library-2.12.15.jar
scala-compiler-2.13.11.jar
scala-reflect-2.13.11.jar
seatunnel-scala-compiler-2.3.13-SNAPSHOT-optional.jar
seatunnel-jackson-2.3.13-SNAPSHOT-optional.jar
seatunnel-guava-2.3.13-SNAPSHOT-optional.jar
seatunnel-hazelcast-shade-2.3.13-SNAPSHOT-optional.jar
slf4j-api-1.7.36.jar
jsqlparser-4.9.jar
animal-sniffer-annotations-1.17.jar
checker-qual-3.10.0.jar
error_prone_annotations-2.2.0.jar
failureaccess-1.0.jar
j2objc-annotations-1.1.jar
jsr305-1.3.9.jar
jsr305-3.0.0.jar
jsr305-3.0.2.jar
listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar
json-path-2.7.0.jar
json-smart-2.4.7.jar
accessors-smart-2.4.7.jar
asm-9.1.jar
avro-1.11.1.jar
groovy-4.0.16.jar
seatunnel-janino-2.3.13-SNAPSHOT-optional.jar
protobuf-java-util-3.25.3.jar
protobuf-java-3.25.3.jar
protoc-jar-3.11.4.jar
error_prone_annotations-2.18.0.jar
gson-2.8.9.jar
j2objc-annotations-2.8.jar
simpleclient-0.16.0.jar
simpleclient_common-0.16.0.jar
simpleclient_hotspot-0.16.0.jar
simpleclient_httpserver-0.16.0.jar
simpleclient_tracer_common-0.16.0.jar
simpleclient_tracer_otel-0.16.0.jar
simpleclient_tracer_otel_agent-0.16.0.jar
jetty-http-9.4.56.v20240826.jar
jetty-io-9.4.56.v20240826.jar
jetty-security-9.4.56.v20240826.jar
jetty-server-9.4.56.v20240826.jar
jetty-servlet-9.4.56.v20240826.jar
jetty-util-9.4.20.v20190813.jar
jetty-util-9.4.56.v20240826.jar
jetty-util-ajax-9.4.56.v20240826.jar
javax.servlet-api-3.1.0.jar
seatunnel-jetty9-9.4.56-2.3.13-SNAPSHOT-optional.jar
jna-5.13.0.jar
jna-5.15.0.jar
jna-platform-5.15.0.jar
oshi-core-6.6.5.jar
arrow-format-15.0.1.jar
arrow-memory-core-15.0.1.jar
arrow-memory-netty-15.0.1.jar
arrow-vector-15.0.1.jar
eclipse-collections-11.1.0.jar
eclipse-collections-api-11.1.0.jar
flatbuffers-java-23.5.26.jar
netty-buffer-4.1.104.Final.jar
netty-common-4.1.104.Final.jar
seatunnel-arrow-2.3.13-SNAPSHOT-optional.jar
sdk-core-2.31.30.jar
third-party-jackson-core-2.31.30.jar
utils-2.31.30.jar
reactive-streams-1.0.4.jar
regions-2.31.30.jar
retries-2.31.30.jar
retries-spi-2.31.30.jar
auth-2.31.30.jar
annotations-2.31.30.jar
apache-client-2.31.30.jar
aws-core-2.31.30.jar
aws-json-protocol-2.31.30.jar
bedrockruntime-2.31.30.jar
checksums-2.31.30.jar
checksums-spi-2.31.30.jar
endpoints-spi-2.31.30.jar
http-auth-2.31.30.jar
http-auth-aws-2.31.30.jar
http-auth-aws-eventstream-2.31.30.jar
http-auth-spi-2.31.30.jar
http-client-spi-2.31.30.jar
identity-spi-2.31.30.jar
json-utils-2.31.30.jar
metrics-spi-2.31.30.jar
netty-nio-client-2.31.30.jar
profiles-2.31.30.jar
protocol-core-2.31.30.jar
netty-transport-4.1.118.Final.jar
netty-transport-classes-epoll-4.1.118.Final.jar
netty-transport-native-unix-common-4.1.118.Final.jar
netty-buffer-4.1.118.Final.jar
netty-codec-4.1.118.Final.jar
netty-codec-http-4.1.118.Final.jar
netty-codec-http2-4.1.118.Final.jar
netty-common-4.1.118.Final.jar
netty-handler-4.1.118.Final.jar
netty-resolver-4.1.118.Final.jar
eventstream-1.0.1.jar
java-diff-utils-4.12.jar
jline-3.22.0.jar
