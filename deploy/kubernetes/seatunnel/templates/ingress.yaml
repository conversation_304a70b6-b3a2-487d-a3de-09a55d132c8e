#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
{{- if and .Values.ingress.enabled }}
{{- if .Capabilities.APIVersions.Has "networking.k8s.io/v1/Ingress" }}
apiVersion: networking.k8s.io/v1
{{- else if .Capabilities.APIVersions.Has "networking.k8s.io/v1beta1/Ingress" }}
apiVersion: networking.k8s.io/v1beta1
{{- else }}
apiVersion: extensions/v1beta1
{{- end }}
kind: Ingress
metadata:
  name: {{ include "seatunnel.fullname" . }}
  labels:
    app.kubernetes.io/name: {{ include "seatunnel.fullname" . }}
    {{- include "seatunnel.common.labels" . | nindent 4 }}
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if .Values.ingress.className }}
  ingressClassName: {{ .Values.ingress.className }}
  {{- end }}
  rules:
  - host: "{{ .Values.ingress.host }}"
    http:
      paths:
        - path: {{ .Values.ingress.path }}
          backend:
            {{- if .Capabilities.APIVersions.Has "networking.k8s.io/v1/Ingress" }}
            service:
              name: {{ include "seatunnel.fullname" . }}-master
              port:
                number: 8080
            {{- else }}
            serviceName: {{ include "seatunnel.fullname" . }}-master
            servicePort: 8080
            {{- end }}
          {{- if .Capabilities.APIVersions.Has "networking.k8s.io/v1/Ingress" }}
          pathType: Prefix
          {{- end }}
  {{- if .Values.ingress.tls.enabled }}
  tls:
    - hosts:
      - {{ .Values.ingress.host }}
      secretName: {{ .Values.ingress.tls.secretName }}
  {{- end }}
{{- end }}
