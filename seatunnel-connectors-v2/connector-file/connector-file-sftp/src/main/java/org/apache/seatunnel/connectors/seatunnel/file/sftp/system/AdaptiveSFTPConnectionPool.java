/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.file.sftp.system;

import com.jcraft.jsch.ChannelSftp;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 自适应SFTP连接池
 * 根据网络状况动态调整连接策略
 */
@Slf4j
public class AdaptiveSFTPConnectionPool extends SFTPConnectionPool {
    
    private final AtomicInteger slowConnections = new AtomicInteger(0);
    private final AtomicLong lastSlowDetection = new AtomicLong(0);
    private final long SLOW_THRESHOLD_MS = 5000; // 5秒认为是慢连接
    private final long RECOVERY_INTERVAL_MS = 60000; // 1分钟后尝试恢复
    
    public AdaptiveSFTPConnectionPool(int maxConnection, int liveConnectionCount) {
        super(maxConnection, liveConnectionCount);
    }
    
    @Override
    synchronized ChannelSftp getFromPool(ConnectionInfo info) throws IOException {
        // 检查是否需要限制连接
        if (shouldLimitConnections()) {
            // 强制等待，避免创建过多连接
            try {
                Thread.sleep(1000); // 等待1秒
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new IOException("Interrupted while waiting for connection");
            }
        }
        
        long startTime = System.currentTimeMillis();
        ChannelSftp channel = super.getFromPool(info);
        long duration = System.currentTimeMillis() - startTime;
        
        // 监控连接获取时间
        if (duration > SLOW_THRESHOLD_MS) {
            int slowCount = slowConnections.incrementAndGet();
            lastSlowDetection.set(System.currentTimeMillis());
            log.warn("Slow SFTP connection detected: {}ms, total slow connections: {}", duration, slowCount);
        }
        
        return channel;
    }
    
    @Override
    ChannelSftp connect(ConnectionInfo info) throws IOException {
        // 如果检测到网络问题，减少并发
        if (shouldLimitConnections()) {
            synchronized (this) {
                // 双重检查，避免创建过多连接
                if (getLiveConnCount() >= 1) {
                    log.warn("Network congestion detected, limiting to 1 connection");
                    throw new IOException("Network congestion detected, please retry later");
                }
            }
        }
        
        long startTime = System.currentTimeMillis();
        ChannelSftp channel = super.connect(info);
        long duration = System.currentTimeMillis() - startTime;
        
        if (duration > SLOW_THRESHOLD_MS) {
            slowConnections.incrementAndGet();
            lastSlowDetection.set(System.currentTimeMillis());
            log.warn("Slow SFTP connection establishment: {}ms", duration);
        }
        
        return channel;
    }
    
    private boolean shouldLimitConnections() {
        long timeSinceLastSlow = System.currentTimeMillis() - lastSlowDetection.get();
        boolean hasRecentSlowConnections = timeSinceLastSlow < RECOVERY_INTERVAL_MS;
        boolean tooManySlowConnections = slowConnections.get() > 3;
        
        if (hasRecentSlowConnections && tooManySlowConnections) {
            return true;
        }
        
        // 恢复期：重置计数器
        if (timeSinceLastSlow > RECOVERY_INTERVAL_MS) {
            slowConnections.set(0);
        }
        
        return false;
    }
    
    public void resetSlowConnectionCounter() {
        slowConnections.set(0);
        lastSlowDetection.set(0);
        log.info("Reset slow connection counter");
    }
    
    public int getSlowConnectionCount() {
        return slowConnections.get();
    }
}
