/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.engine.server.execution;

import org.apache.seatunnel.engine.server.task.group.TaskGroupWithIntermediateBlockingQueue;
import org.apache.seatunnel.engine.server.task.group.TaskGroupWithIntermediateDisruptor;

import java.util.Collection;

public class TaskGroupUtils {

    public static TaskGroup createTaskGroup(
            TaskGroupType type,
            TaskGroupLocation taskGroupLocation,
            String taskGroupName,
            Collection<Task> tasks) {
        switch (type) {
            case DEFAULT:
                return new TaskGroupDefaultImpl(taskGroupLocation, taskGroupName, tasks);
            case INTERMEDIATE_BLOCKING_QUEUE:
                return new TaskGroupWithIntermediateBlockingQueue(
                        taskGroupLocation, taskGroupName, tasks);
            case INTERMEDIATE_DISRUPTOR_QUEUE:
                return new TaskGroupWithIntermediateDisruptor(
                        taskGroupLocation, taskGroupName, tasks);
            default:
                throw new IllegalArgumentException("Unsupported task group type: " + type);
        }
    }
}
