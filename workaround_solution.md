# SeaTunnel SFTP并发问题临时解决方案

## 1. 立即可用的配置调整

### 方案A：降低并发度
```hocon
env {
  execution.parallelism = 1
  parallelism = 1
  # 其他配置保持不变
}
```

### 方案B：分离多表作业
将原来的多表同步作业拆分为多个单表作业，分别运行：

```bash
# 作业1：同步表1
./bin/seatunnel.sh --config config/table1_to_sftp.conf

# 作业2：同步表2  
./bin/seatunnel.sh --config config/table2_to_sftp.conf

# 依此类推...
```

### 方案C：增加重试和错误处理
```hocon
sink {
  SftpFile {
    # 增加重试次数
    max_retries = 5
    
    # 增加连接超时
    connection_check_timeout_sec = 60
    
    # 启用事务
    is_enable_transaction = true
  }
}
```

## 2. 系统级优化

### 调整SFTP服务器配置
在SFTP服务器端增加以下配置：

```bash
# /etc/ssh/sshd_config
MaxSessions 20
MaxStartups 20:30:100
ClientAliveInterval 60
ClientAliveCountMax 3
```

### 调整SeaTunnel JVM参数
```bash
export JAVA_OPTS="-Xmx4g -Xms2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
```

## 3. 监控和诊断

### 添加详细日志
在 `conf/log4j2.properties` 中添加：
```properties
logger.sftp.name = org.apache.seatunnel.connectors.seatunnel.file.sftp
logger.sftp.level = DEBUG
logger.sftp.additivity = false
logger.sftp.appenderRef.console.ref = consoleAppender
```

### 监控脚本
```bash
#!/bin/bash
# monitor_sftp_connections.sh

while true; do
    echo "$(date): Checking SFTP connections..."
    netstat -an | grep :22 | grep ESTABLISHED | wc -l
    sleep 10
done
```

## 4. 根本解决方案

### 修复SFTPConnectionPool.java
将提供的 `SFTPConnectionPoolFixed.java` 替换原文件：

```bash
# 备份原文件
cp seatunnel-connectors-v2/connector-file/connector-file-sftp/src/main/java/org/apache/seatunnel/connectors/seatunnel/file/sftp/system/SFTPConnectionPool.java \
   SFTPConnectionPool.java.backup

# 替换为修复版本
cp SFTPConnectionPoolFixed.java \
   seatunnel-connectors-v2/connector-file/connector-file-sftp/src/main/java/org/apache/seatunnel/connectors/seatunnel/file/sftp/system/SFTPConnectionPool.java

# 重新编译
mvn clean package -DskipTests
```

## 5. 验证解决方案

### 测试脚本
```bash
#!/bin/bash
# test_concurrent_sync.sh

echo "Starting concurrent SFTP sync test..."

# 启动多个测试作业
for i in {1..5}; do
    echo "Starting job $i..."
    ./bin/seatunnel.sh --config config/test_table_$i.conf &
    sleep 2
done

# 等待所有作业完成
wait

echo "All jobs completed. Check logs for errors."
```

### 检查结果
```bash
# 检查是否有FileNotFoundException错误
grep -i "FileNotFoundException" logs/seatunnel-engine-server.log

# 检查SFTP连接状态
grep -i "sftp.*connection" logs/seatunnel-engine-server.log

# 检查作业完成状态
grep -i "job.*finished" logs/seatunnel-engine-server.log
```

## 6. 预防措施

1. **定期重启**: 每天重启SeaTunnel服务清理连接池
2. **监控告警**: 设置SFTP连接数和错误率监控
3. **资源限制**: 限制单个作业的最大并发数
4. **分批处理**: 将大批量数据分成小批次处理
5. **错误恢复**: 实现自动重试和故障恢复机制

## 7. 长期建议

1. 升级到最新版本的SeaTunnel
2. 考虑使用其他文件传输协议（如S3、HDFS）
3. 实现自定义的SFTP连接池
4. 使用消息队列缓冲数据传输
5. 考虑使用分布式文件系统
