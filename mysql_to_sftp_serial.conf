env {
  # 完全串行处理，避免网络阻塞
  execution.parallelism = 1
  parallelism = 1
  job.mode = "BATCH"
  checkpoint.interval = 30000
  job.timeout = 7200000  # 2小时超时
}

source {
  Jdbc {
    url = "***********************************************"
    driver = "com.mysql.cj.jdbc.Driver"
    username = "your_username"
    password = "your_password"
    
    # 分批查询，减少内存压力
    query = "select * from your_test_table"
    fetch_size = 1000
    
    # 连接池配置
    connection_check_timeout_sec = 60
    max_retries = 5
    
    plugin_output = "mysql_source"
  }
}

sink {
  SftpFile {
    host = "your-sftp-host"
    port = 22
    user = "your_username"
    password = "your_password"
    
    path = "/tmp/seatunnel/serial_${now}"
    plugin_input = "mysql_source"
    
    file_format_type = "text"
    row_delimiter = "\n"
    field_delimiter = ","
    
    # 启用事务
    is_enable_transaction = true
    
    # 文件名配置
    file_name_expression = "serial_${transactionId}_${now}"
    filename_time_format = "yyyy-MM-dd-HH-mm-ss"
    
    # 数据保存模式
    schema_save_mode = "CREATE_SCHEMA_WHEN_NOT_EXIST"
    data_save_mode = "APPEND_DATA"
    
    # 大幅减少批次大小，频繁刷新
    batch_size = 500
    
    # 启用单文件模式，减少文件操作
    single_file_mode = true
    
    # SFTP连接优化配置
    connection_check_timeout_sec = 120
    max_retries = 10
    
    # 最保守的连接配置
    hadoop_conf = {
      "fs.sftp.connection.max" = "1"      # 只使用1个连接
      "fs.sftp.impl" = "org.apache.seatunnel.connectors.seatunnel.file.sftp.system.SFTPFileSystem"
      "fs.sftp.timeout" = "120000"        # 2分钟超时
      "fs.sftp.buffer.size" = "16384"     # 16KB缓冲区
      "fs.sftp.retry.max" = "5"           # 最大重试5次
      "fs.sftp.retry.interval" = "5000"   # 重试间隔5秒
    }
  }
}
