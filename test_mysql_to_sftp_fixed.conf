env {
  # 使用修复后的配置
  execution.parallelism = 2
  parallelism = 2
  job.mode = "BATCH"
  checkpoint.interval = 10000
}

source {
  Jdbc {
    url = "***********************************************"
    driver = "com.mysql.cj.jdbc.Driver"
    username = "your_username"
    password = "your_password"
    
    # 测试查询
    query = "select * from your_test_table limit 1000"
    
    # 连接池配置
    connection_check_timeout_sec = 30
    max_retries = 3
    
    plugin_output = "mysql_source"
  }
}

sink {
  SftpFile {
    host = "your-sftp-host"
    port = 22
    user = "your_username"
    password = "your_password"
    
    # 测试路径
    path = "/tmp/seatunnel/test_fixed/${now}"
    plugin_input = "mysql_source"
    
    file_format_type = "text"
    row_delimiter = "\n"
    field_delimiter = ","
    
    # 启用事务
    is_enable_transaction = true
    
    # 文件名配置
    file_name_expression = "test_${transactionId}_${now}"
    filename_time_format = "yyyy-MM-dd-HH-mm-ss"
    
    # 数据保存模式
    schema_save_mode = "CREATE_SCHEMA_WHEN_NOT_EXIST"
    data_save_mode = "APPEND_DATA"
    
    # SFTP连接优化配置
    connection_check_timeout_sec = 30
    max_retries = 3
    
    # Hadoop配置优化SFTP连接池
    hadoop_conf = {
      "fs.sftp.connection.max" = "5"  # 适当增加连接数
      "fs.sftp.impl" = "org.apache.seatunnel.connectors.seatunnel.file.sftp.system.SFTPFileSystem"
    }
  }
}
