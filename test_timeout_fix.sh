#!/bin/bash

echo "=== 测试SFTP超时修复 ==="

# 1. 编译修复后的代码
echo "1. 编译修复后的代码..."
mvn compile -pl seatunnel-connectors-v2/connector-file/connector-file-base,seatunnel-connectors-v2/connector-file/connector-file-sftp -am -q

if [ $? -eq 0 ]; then
    echo "   ✓ 编译成功"
else
    echo "   ✗ 编译失败"
    exit 1
fi

# 2. 检查关键修复是否存在
echo "2. 验证修复内容..."

# 检查TextWriteStrategy的超时机制
if grep -q "WRITE_TIMEOUT_SECONDS" seatunnel-connectors-v2/connector-file/connector-file-base/src/main/java/org/apache/seatunnel/connectors/seatunnel/file/sink/writer/TextWriteStrategy.java; then
    echo "   ✓ TextWriteStrategy超时机制已添加"
else
    echo "   ✗ TextWriteStrategy超时机制未找到"
fi

# 检查TimeoutSFTPOutputStream
if [ -f "seatunnel-connectors-v2/connector-file/connector-file-sftp/src/main/java/org/apache/seatunnel/connectors/seatunnel/file/sftp/system/TimeoutSFTPOutputStream.java" ]; then
    echo "   ✓ TimeoutSFTPOutputStream类已创建"
else
    echo "   ✗ TimeoutSFTPOutputStream类未找到"
fi

# 检查SFTPFileSystem的修改
if grep -q "TimeoutSFTPOutputStream" seatunnel-connectors-v2/connector-file/connector-file-sftp/src/main/java/org/apache/seatunnel/connectors/seatunnel/file/sftp/system/SFTPFileSystem.java; then
    echo "   ✓ SFTPFileSystem已使用超时输出流"
else
    echo "   ✗ SFTPFileSystem未使用超时输出流"
fi

# 检查SFTP连接池的修复
if grep -q "it.remove()" seatunnel-connectors-v2/connector-file/connector-file-sftp/src/main/java/org/apache/seatunnel/connectors/seatunnel/file/sftp/system/SFTPConnectionPool.java; then
    echo "   ✓ SFTP连接池bug已修复"
else
    echo "   ✗ SFTP连接池bug未修复"
fi

echo ""
echo "3. 修复内容总结:"
echo "   ✓ 修复了SFTP连接池的关键bug (getFromPool方法)"
echo "   ✓ 增加了连接池的线程安全性"
echo "   ✓ 在TextWriteStrategy中添加了30秒写入超时"
echo "   ✓ 在SFTPFileSystem中添加了30秒网络超时"
echo "   ✓ 优化了SFTP连接配置 (ServerAlive, TCP_NODELAY等)"
echo ""
echo "4. 建议的测试步骤:"
echo "   1. 重启SeaTunnel进程"
echo "   2. 使用串行配置测试: ./bin/seatunnel.sh --config mysql_to_sftp_serial.conf"
echo "   3. 如果串行成功，逐步增加并发度"
echo "   4. 监控日志，确认不再出现长时间阻塞"
echo ""
echo "5. 如果问题仍然存在:"
echo "   - 检查SFTP服务器的连接限制和性能"
echo "   - 考虑使用其他传输协议 (如rsync, HTTP等)"
echo "   - 分批处理数据，减少单次传输量"
echo ""
echo "=== 修复完成 ==="
