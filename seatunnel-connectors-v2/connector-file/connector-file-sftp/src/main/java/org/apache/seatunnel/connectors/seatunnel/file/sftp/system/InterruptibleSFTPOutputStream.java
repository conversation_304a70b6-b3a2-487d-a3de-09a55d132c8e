/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.file.sftp.system;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.Session;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.OutputStream;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 可中断的SFTP输出流
 * 通过监控写入时间和强制中断来解决网络阻塞问题
 */
@Slf4j
public class InterruptibleSFTPOutputStream extends OutputStream {
    
    private final OutputStream delegate;
    private final ChannelSftp channel;
    private final Session session;
    private final AtomicBoolean closed = new AtomicBoolean(false);
    private final AtomicLong lastWriteTime = new AtomicLong(System.currentTimeMillis());
    private final long maxIdleTimeMs = 30000; // 30秒无响应则强制中断
    
    // 监控线程
    private final Thread watchdog;
    private volatile boolean stopWatchdog = false;
    
    public InterruptibleSFTPOutputStream(OutputStream delegate, ChannelSftp channel) {
        this.delegate = delegate;
        this.channel = channel;
        this.session = channel.getSession();
        
        // 启动看门狗线程
        this.watchdog = new Thread(this::watchdogLoop, "sftp-watchdog-" + System.currentTimeMillis());
        this.watchdog.setDaemon(true);
        this.watchdog.start();
    }
    
    private void watchdogLoop() {
        while (!stopWatchdog && !closed.get()) {
            try {
                Thread.sleep(5000); // 每5秒检查一次
                
                long idleTime = System.currentTimeMillis() - lastWriteTime.get();
                if (idleTime > maxIdleTimeMs) {
                    log.warn("SFTP write operation has been idle for {} ms, forcing disconnect", idleTime);
                    forceDisconnect();
                    break;
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
    
    private void forceDisconnect() {
        try {
            log.warn("Force disconnecting SFTP session due to timeout");
            if (channel != null && channel.isConnected()) {
                channel.disconnect();
            }
            if (session != null && session.isConnected()) {
                session.disconnect();
            }
        } catch (Exception e) {
            log.error("Error during force disconnect", e);
        }
    }
    
    @Override
    public void write(int b) throws IOException {
        if (closed.get()) {
            throw new IOException("Stream is closed");
        }
        
        updateLastWriteTime();
        try {
            delegate.write(b);
        } catch (IOException e) {
            handleWriteError(e);
            throw e;
        }
    }
    
    @Override
    public void write(byte[] b) throws IOException {
        if (closed.get()) {
            throw new IOException("Stream is closed");
        }
        
        updateLastWriteTime();
        try {
            delegate.write(b);
        } catch (IOException e) {
            handleWriteError(e);
            throw e;
        }
    }
    
    @Override
    public void write(byte[] b, int off, int len) throws IOException {
        if (closed.get()) {
            throw new IOException("Stream is closed");
        }
        
        updateLastWriteTime();
        try {
            delegate.write(b, off, len);
        } catch (IOException e) {
            handleWriteError(e);
            throw e;
        }
    }
    
    @Override
    public void flush() throws IOException {
        if (closed.get()) {
            throw new IOException("Stream is closed");
        }
        
        updateLastWriteTime();
        try {
            delegate.flush();
        } catch (IOException e) {
            handleWriteError(e);
            throw e;
        }
    }
    
    @Override
    public void close() throws IOException {
        if (closed.compareAndSet(false, true)) {
            stopWatchdog = true;
            if (watchdog != null) {
                watchdog.interrupt();
            }
            
            try {
                delegate.close();
            } catch (IOException e) {
                log.warn("Error closing delegate stream", e);
                throw e;
            }
        }
    }
    
    private void updateLastWriteTime() {
        lastWriteTime.set(System.currentTimeMillis());
    }
    
    private void handleWriteError(IOException e) {
        log.error("SFTP write error: {}", e.getMessage());
        // 检查是否是连接问题
        if (!channel.isConnected() || !session.isConnected()) {
            log.warn("SFTP connection lost during write operation");
        }
    }
}
