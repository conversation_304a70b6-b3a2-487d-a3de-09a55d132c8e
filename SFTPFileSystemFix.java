// 修复SFTPFileSystem的并发问题
// 在seatunnel-connectors-v2/connector-file/connector-file-sftp/src/main/java/org/apache/seatunnel/connectors/seatunnel/file/sftp/system/SFTPFileSystem.java

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

public class SFTPFileSystem extends FileSystem {
    
    // 添加目录创建锁，避免并发创建同一目录
    private static final ConcurrentHashMap<String, ReentrantLock> directoryLocks = new ConcurrentHashMap<>();
    
    /**
     * 线程安全的目录创建方法
     */
    private boolean mkdirs(ChannelSftp client, Path file, FsPermission permission) throws IOException {
        String absolutePath = makeAbsolute(getWorkingDirectory(client), file).toString();
        
        // 获取或创建目录锁
        ReentrantLock lock = directoryLocks.computeIfAbsent(absolutePath, k -> new ReentrantLock());
        
        lock.lock();
        try {
            return mkdirsInternal(client, file, permission);
        } finally {
            lock.unlock();
            // 清理不再需要的锁
            if (lock.getQueueLength() == 0 && !lock.isLocked()) {
                directoryLocks.remove(absolutePath, lock);
            }
        }
    }
    
    /**
     * 内部目录创建逻辑
     */
    private boolean mkdirsInternal(ChannelSftp client, Path file, FsPermission permission) throws IOException {
        boolean created = true;
        Path workDir;
        try {
            workDir = new Path(client.pwd());
        } catch (SftpException e) {
            throw new IOException(e);
        }
        
        Path absolute = makeAbsolute(workDir, file);
        String pathName = absolute.getName();
        
        // 双重检查：再次检查目录是否存在
        if (!exists(client, absolute)) {
            Path parent = absolute.getParent();
            created = parent == null || mkdirs(client, parent, FsPermission.getDefault());
            
            if (created) {
                String parentDir = parent.toUri().getPath();
                try {
                    final String previousCwd = client.pwd();
                    client.cd(parentDir);
                    
                    // 再次检查目录是否已被其他线程创建
                    if (!directoryExists(client, pathName)) {
                        LOG.debug("Creating directory " + pathName);
                        client.mkdir(pathName);
                    } else {
                        LOG.debug("Directory already exists: " + pathName);
                    }
                    
                    client.cd(previousCwd);
                } catch (SftpException e) {
                    // 如果是目录已存在的错误，忽略它
                    if (e.id == ChannelSftp.SSH_FX_FAILURE && directoryExists(client, pathName)) {
                        LOG.debug("Directory creation race condition resolved for: " + pathName);
                    } else {
                        throw new IOException(String.format(E_MAKE_DIR_FORPATH, pathName, parentDir), e);
                    }
                }
            }
        } else if (isFile(client, absolute)) {
            throw new IOException(String.format(E_DIR_CREATE_FROMFILE, absolute));
        } else {
            LOG.debug("Skipping creation of existing directory " + file);
        }
        
        return created;
    }
    
    /**
     * 检查目录是否存在的辅助方法
     */
    private boolean directoryExists(ChannelSftp client, String dirName) {
        try {
            SftpATTRS attrs = client.stat(dirName);
            return attrs.isDir();
        } catch (SftpException e) {
            return false;
        }
    }
    
    /**
     * 获取工作目录的辅助方法
     */
    private Path getWorkingDirectory(ChannelSftp client) throws IOException {
        try {
            return new Path(client.pwd());
        } catch (SftpException e) {
            throw new IOException("Failed to get working directory", e);
        }
    }
}
