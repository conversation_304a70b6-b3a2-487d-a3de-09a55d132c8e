env {
  # 最终测试配置 - 激进的网络优化
  execution.parallelism = 1
  parallelism = 1
  job.mode = "BATCH"
  checkpoint.interval = 60000
  job.timeout = 3600000  # 1小时超时
}

source {
  Jdbc {
    url = "***********************************************"
    driver = "com.mysql.cj.jdbc.Driver"
    username = "your_username"
    password = "your_password"
    
    # 小批量查询，快速测试
    query = "select * from your_test_table limit 10000"
    fetch_size = 1000
    
    # 连接池配置
    connection_check_timeout_sec = 30
    max_retries = 3
    
    plugin_output = "mysql_source"
  }
}

sink {
  SftpFile {
    host = "your-sftp-host"
    port = 22
    user = "your_username"
    password = "your_password"
    
    path = "/tmp/seatunnel/final_test_${now}"
    plugin_input = "mysql_source"
    
    file_format_type = "text"
    row_delimiter = "\n"
    field_delimiter = ","
    
    # 启用事务
    is_enable_transaction = true
    
    # 文件名配置
    file_name_expression = "final_test_${transactionId}_${now}"
    filename_time_format = "yyyy-MM-dd-HH-mm-ss"
    
    # 数据保存模式
    schema_save_mode = "CREATE_SCHEMA_WHEN_NOT_EXIST"
    data_save_mode = "APPEND_DATA"
    
    # 小批次，频繁刷新
    batch_size = 500
    
    # 启用单文件模式
    single_file_mode = true
    
    # SFTP连接优化配置
    connection_check_timeout_sec = 30
    max_retries = 5
    
    # 激进的网络优化配置
    hadoop_conf = {
      "fs.sftp.connection.max" = "3"      # 最少连接数
      "fs.sftp.impl" = "org.apache.seatunnel.connectors.seatunnel.file.sftp.system.SFTPFileSystem"
      "fs.sftp.timeout" = "30000"         # 30秒超时
      "fs.sftp.buffer.size" = "32768"     # 32KB缓冲区
      "fs.sftp.retry.max" = "3"           # 最大重试3次
      "fs.sftp.retry.interval" = "3000"   # 重试间隔3秒
    }
  }
}
