---
default_stages: [commit, push]
default_language_version:
  # force all unspecified python hooks to run python3
  python: python3
minimum_pre_commit_version: "1.20.0"
repos:
  - repo: https://github.com/Lucas-C/pre-commit-hooks
    rev: v1.1.7
    hooks:
      - id: forbid-tabs
        exclude: ^dist/index.js$
  - repo: https://github.com/thlorenz/doctoc.git
    rev: v1.4.0
    hooks:
      - id: doctoc
        name: Add TOC for md files
        files: ^README\.md$|^CONTRIBUTING\.md$|^UPDATING.md$|^dev/README\.md$|^dev/BACKPORT_PACKAGES.md$
  - repo: meta
    hooks:
      - id: check-hooks-apply
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.23.0
    hooks:
      - id: yamllint
        name: Check yaml files with yamllint
        entry: yamllint -c yamllint-config.yml
        types: [yaml]
        exclude: ^.*init_git_sync\.template\.yaml$|^.*airflow\.template\.yaml$|^chart/templates/.*\.yaml$
  - repo: local
    hooks:
      - id: build
        name: Build package for distribution
        language: system
        entry: bash -c "npm run release"
        files: .*\.ts$
        require_serial: true
        pass_filenames: false
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v3.1.0
    hooks:
      - id: check-merge-conflict
      - id: detect-private-key
      - id: end-of-file-fixer
        exclude: ^dist/.*
      - id: trailing-whitespace
        exclude: ^dist/.*
